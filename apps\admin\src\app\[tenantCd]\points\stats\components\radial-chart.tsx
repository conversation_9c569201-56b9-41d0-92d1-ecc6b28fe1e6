"use client";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { ChartConfig, ChartContainer } from "@workspace/ui/components/chart";
import {
  Label,
  PolarGrid,
  PolarRadiusAxis,
  RadialBar,
  RadialBarChart,
} from "recharts";

export const description = "A radial chart with point statistics";

// 5개 항목의 차트 데이터
const chartDataItems = [
  {
    id: "charge",
    title: "포인트 충전 현황",
    description: "충전포인트의 합",
    value: 125000,
    fill: "var(--color-charge)",
  },
  {
    id: "usage",
    title: "포인트 사용 현황",
    description: "사용포인트의 합",
    value: 89000,
    fill: "var(--color-usage)",
  },
  {
    id: "import",
    title: "포인트 전환(가져오기) 현황",
    description: "전환 포인트의 합",
    value: 45000,
    fill: "var(--color-import)",
  },
  {
    id: "export",
    title: "포인트 전환(내보내기) 현황",
    description: "전환포인트의 합(지역화폐 포함)",
    value: 32000,
    fill: "var(--color-export)",
  },
  {
    id: "local",
    title: "지역화폐 전환금액",
    description: "전환포인트의 합",
    value: 78000,
    fill: "var(--color-local)",
  },
];

const chartConfig = {
  value: {
    label: "포인트",
  },
  charge: {
    label: "충전",
    color: "var(--chart-1)",
  },
  usage: {
    label: "사용",
    color: "var(--chart-2)",
  },
  import: {
    label: "가져오기",
    color: "var(--chart-3)",
  },
  export: {
    label: "내보내기",
    color: "var(--chart-4)",
  },
  local: {
    label: "지역화폐",
    color: "var(--chart-5)",
  },
} satisfies ChartConfig;

// 개별 차트 컴포넌트
function SingleRadialChart({ item }: { item: (typeof chartDataItems)[0] }) {
  const chartData = [{ value: item.value, fill: item.fill }];

  return (
    <Card className="flex flex-col">
      <CardHeader className="items-center pb-0">
        <CardTitle className="text-sm font-medium">{item.title}</CardTitle>
        <CardDescription className="text-xs">
          {item.description}
        </CardDescription>
      </CardHeader>
      <CardContent className="flex-1 pb-0 pt-4">
        <ChartContainer
          config={chartConfig}
          className="mx-auto aspect-square max-h-[180px]"
        >
          <RadialBarChart
            data={chartData}
            endAngle={100}
            innerRadius={60}
            outerRadius={100}
          >
            <PolarGrid
              gridType="circle"
              radialLines={false}
              stroke="none"
              className="first:fill-muted last:fill-background"
              polarRadius={[66, 54]}
            />
            <RadialBar dataKey="value" background />
            <PolarRadiusAxis tick={false} tickLine={false} axisLine={false}>
              <Label
                content={({ viewBox }) => {
                  if (viewBox && "cx" in viewBox && "cy" in viewBox) {
                    return (
                      <text
                        x={viewBox.cx}
                        y={viewBox.cy}
                        textAnchor="middle"
                        dominantBaseline="middle"
                      >
                        <tspan
                          x={viewBox.cx}
                          y={viewBox.cy}
                          className="fill-foreground text-2xl font-bold"
                        >
                          {item.value.toLocaleString()}
                        </tspan>
                      </text>
                    );
                  }
                }}
              />
            </PolarRadiusAxis>
          </RadialBarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}

// 5개 차트를 그리드로 표시하는 메인 컴포넌트
export function ChartRadialShape() {
  return (
    <div className="mt-6 grid grid-cols-5 gap-4">
      {chartDataItems.map((item) => (
        <SingleRadialChart key={item.id} item={item} />
      ))}
    </div>
  );
}
